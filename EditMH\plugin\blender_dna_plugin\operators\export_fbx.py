"""
Export FBX Operator for the Blender MetaHuman DNA Plugin.

This module provides functionality to export individual meshes with proper
armature relationships for Unreal Engine import, following the standard
MetaHuman workflow instead of combining meshes.
"""

import os
import bpy
import time
import traceback
from bpy.props import StringProperty, BoolProperty
from mathutils import Vector, Matrix

# Import the DNA utils
from ..utils.dna_utils import check_dna_modules, ensure_dna_modules_path
from ..utils.ui_utils import update_ui
from ..utils.rotation_utils import FBX_EXPORT_SCALE_FACTOR

# Set up logging
def log_info(message):
    """Log an info message with timestamp"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    print(f"[{timestamp}] [INFO] {message}")

def log_warning(message):
    """Log a warning message with timestamp"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    print(f"[{timestamp}] [WARNING] {message}")

def log_error(message):
    """Log an error message with timestamp"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    print(f"[{timestamp}] [ERROR] {message}")

def log_debug(message):
    """Log a debug message with timestamp"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    print(f"[{timestamp}] [DEBUG] {message}")


class DNA_OT_ExportFBX(bpy.types.Operator):
    """Export FBX file for Unreal Engine"""
    bl_idname = "dna.export_fbx"
    bl_label = "Export FBX for Unreal"
    bl_description = "Export FBX file for Unreal Engine import"
    bl_options = {'REGISTER', 'UNDO'}

    # File browser properties
    filepath: StringProperty(
        name="File Path",
        description="Path to export the files",
        default="//export/",
        subtype='DIR_PATH'
    )

    def invoke(self, context, event):
        """Invoke the file browser"""
        context.window_manager.fileselect_add(self)
        return {'RUNNING_MODAL'}

    def execute(self, context):
        """Execute the export operation"""
        log_info("=== DNA Export FBX Process Started ===")

        # Update status message
        context.scene.dna_tools.status_message = "Exporting FBX and DNA files..."
        update_ui()

        # Check if DNA is loaded
        dna_tools = context.scene.dna_tools
        if not dna_tools.is_dna_loaded:
            log_error("No DNA file loaded")
            context.scene.dna_tools.status_message = "Error: No DNA file loaded"
            update_ui()
            self.report({'ERROR'}, "No DNA file loaded. Import a DNA file first.")
            return {'CANCELLED'}

        # Check if mesh has been created
        if not dna_tools.is_mesh_created:
            log_error("No mesh created")
            context.scene.dna_tools.status_message = "Error: No mesh created"
            update_ui()
            self.report({'ERROR'}, "No mesh created. Create a mesh first.")
            return {'CANCELLED'}

        try:
            # Get export directory from filepath
            export_dir = os.path.dirname(self.filepath)
            if not export_dir:
                export_dir = bpy.path.abspath("//export/")

            # Ensure export directory exists
            os.makedirs(export_dir, exist_ok=True)
            log_info(f"Export directory: {export_dir}")

            # Get model name from DNA file
            dna_file_path = dna_tools.dna_file_path
            model_name = os.path.splitext(os.path.basename(dna_file_path))[0]
            log_info(f"Model name: {model_name}")

            # Export FBX file only
            log_info("Exporting FBX file...")
            context.scene.dna_tools.status_message = "Exporting FBX file..."
            update_ui()

            fbx_export_path = os.path.join(export_dir, f"{model_name}.fbx")
            export_collection, export_armature = self.export_fbx_file(context, fbx_export_path, model_name)

            # Restore original armature name (no matrix scale restoration needed)
            if export_armature:
                self.restore_armature_name(context, export_armature)

            # Clean up export collection after successful export
            # NOTE: This must happen AFTER MetaHuman hierarchy cleanup to avoid double-removal
            if export_collection:
                self.cleanup_export_collection(context, export_collection)

            log_info("FBX export completed successfully")
            context.scene.dna_tools.status_message = f"FBX export completed: {export_dir}"
            update_ui()

            self.report({'INFO'}, f"FBX export completed successfully to: {export_dir}")
            return {'FINISHED'}

        except Exception as e:
            log_error(f"Error during FBX export: {str(e)}")
            log_error(f"Error type: {type(e).__name__}")
            import traceback
            log_error(f"Full traceback: {traceback.format_exc()}")
            context.scene.dna_tools.status_message = f"Error during FBX export: {str(e)}"
            update_ui()
            self.report({'ERROR'}, f"Error during FBX export: {str(e)}")
            return {'CANCELLED'}



    def export_fbx_file(self, context, export_path, model_name):
        """Export the FBX file with individual meshes and clean armature"""
        log_info(f"Exporting FBX file to: {export_path}")

        # Store original selection and active object
        original_selection = context.selected_objects.copy()
        original_active = context.active_object
        original_mode = context.mode

        try:
            # Ensure we're in object mode
            if context.mode != 'OBJECT':
                bpy.ops.object.mode_set(mode='OBJECT')

            # Find the model collection and all mesh objects
            model_collection = None
            mesh_objects = []
            armature_obj = None

            # First, try to find the main model collection
            for collection in bpy.data.collections:
                if collection.name == model_name:
                    model_collection = collection
                    break

            if not model_collection:
                raise Exception(f"Model collection '{model_name}' not found")

            # Get mesh objects from the main collection and all sub-collections (LOD collections)
            def collect_meshes_from_collection(collection):
                """Recursively collect mesh objects from a collection and its children"""
                meshes = []

                # Get meshes from this collection
                for obj in collection.objects:
                    if obj.type == 'MESH':
                        meshes.append(obj)

                # Get meshes from child collections (LOD collections)
                for child_collection in collection.children:
                    meshes.extend(collect_meshes_from_collection(child_collection))

                return meshes

            # Collect all mesh objects from the model collection and its children
            mesh_objects = collect_meshes_from_collection(model_collection)
            log_info(f"Found {len(mesh_objects)} mesh objects to export")

            if not mesh_objects:
                # Try to find meshes in any collection that starts with the model name
                log_warning(f"No meshes found in main collection, searching in collections starting with '{model_name}'")
                for collection in bpy.data.collections:
                    if collection.name.startswith(model_name):
                        collection_meshes = [obj for obj in collection.objects if obj.type == 'MESH']
                        mesh_objects.extend(collection_meshes)
                        log_info(f"Found {len(collection_meshes)} meshes in collection '{collection.name}'")

                if not mesh_objects:
                    raise Exception(f"No mesh objects found in any collection related to '{model_name}'")

            # Get armature object from the main collection or its children
            def find_armature_in_collection(collection):
                """Recursively find armature in a collection and its children"""
                # Check this collection
                for obj in collection.objects:
                    if obj.type == 'ARMATURE':
                        return obj

                # Check child collections
                for child_collection in collection.children:
                    armature = find_armature_in_collection(child_collection)
                    if armature:
                        return armature

                return None

            armature_obj = find_armature_in_collection(model_collection)

            if not armature_obj:
                # Try to find armature in any collection that starts with the model name
                log_warning(f"No armature found in main collection, searching in collections starting with '{model_name}'")
                for collection in bpy.data.collections:
                    if collection.name.startswith(model_name):
                        for obj in collection.objects:
                            if obj.type == 'ARMATURE':
                                armature_obj = obj
                                break
                        if armature_obj:
                            break

                if not armature_obj:
                    raise Exception(f"No armature found in any collection related to '{model_name}'")

            # Create export collection to keep things organized
            export_collection = self.create_export_collection(context, model_name)

            # Use the existing armature and rename it to 'root' for export
            export_armature = self.prepare_armature_for_export(context, armature_obj, export_collection)

            # NOTE: Objects already have correct 0.01 matrix scale applied during DNA import
            # Do NOT apply additional scaling to avoid 100x smaller result (0.01 * 0.01 = 0.0001)

            # Combine all LOD0 meshes into a single mesh like the original MetaHuman FBX
            export_mesh = self.prepare_combined_mesh(context, mesh_objects, export_collection, export_armature)

            # Ensure we're in object mode before selection operations
            if context.mode != 'OBJECT':
                bpy.ops.object.mode_set(mode='OBJECT')

            # Clear selection manually instead of using select_all
            for obj in context.selected_objects:
                obj.select_set(False)

            # Create MetaHuman hierarchy for export (with compensation)
            parent_empty, original_armature_state = self.create_metahuman_export_hierarchy(
                export_armature, export_collection
            )

            # Select objects for export (parent EMPTY, armature and combined mesh)
            parent_empty.select_set(True)
            export_armature.select_set(True)
            export_mesh.select_set(True)
            context.view_layer.objects.active = export_armature

            # Export FBX
            log_info("Exporting FBX file with MetaHuman hierarchy...")
            log_info(f"Exporting combined mesh: {export_mesh.name}")
            log_info(f"Selected objects for export: {[obj.name for obj in context.selected_objects]}")
            log_info(f"Active object: {context.view_layer.objects.active.name if context.view_layer.objects.active else 'None'}")

            try:
                # Export FBX with current object scales (no additional scaling applied)
                # Objects imported from DNA already have 0.01 matrix scale applied during import
                bpy.ops.export_scene.fbx(
                    filepath=export_path,
                    use_selection=True,
                    object_types={'EMPTY', 'ARMATURE', 'MESH'},  # Include EMPTY for parent hierarchy
                    use_mesh_modifiers=True,
                    mesh_smooth_type='FACE',
                    use_armature_deform_only=True,
                    add_leaf_bones=False,
                    # Bone axis settings for MetaHuman compatibility
                    primary_bone_axis='Y',
                    secondary_bone_axis='X',
                    armature_nodetype='NULL',
                    bake_anim=False,
                    # Use normal scale - preserve existing object transformations
                    global_scale=1.0,
                    apply_unit_scale=True,
                    apply_scale_options='FBX_SCALE_NONE',
                    # Standard axis settings for FBX
                    axis_forward='-Z',
                    axis_up='Y'
                )

                # Check if FBX file was actually created
                if os.path.exists(export_path):
                    file_size = os.path.getsize(export_path)
                    log_info(f"FBX file exported successfully: {export_path} ({file_size} bytes)")
                else:
                    raise Exception(f"FBX file was not created at: {export_path}")

            except Exception as fbx_error:
                log_error(f"FBX export failed: {str(fbx_error)}")
                raise Exception(f"FBX export failed: {str(fbx_error)}")

            finally:
                # Guaranteed cleanup - restore original armature state
                self.cleanup_metahuman_export_hierarchy(
                    export_armature, parent_empty, original_armature_state
                )

            # Return both the export collection and armature for cleanup by caller
            return export_collection, export_armature

        finally:
            # Restore original selection manually
            for obj in context.selected_objects:
                obj.select_set(False)

            for obj in original_selection:
                if obj.name in bpy.data.objects:
                    obj.select_set(True)

            if original_active and original_active.name in bpy.data.objects:
                context.view_layer.objects.active = original_active

            # Restore original mode if it was different
            if original_mode != 'OBJECT' and context.mode == 'OBJECT':
                try:
                    bpy.ops.object.mode_set(mode=original_mode)
                except:
                    log_warning(f"Could not restore original mode: {original_mode}")

    def create_export_collection(self, context, model_name):
        """Create a temporary collection for export objects"""
        export_collection_name = f"{model_name}_Export_Temp"

        # Remove existing export collection if it exists
        if export_collection_name in bpy.data.collections:
            old_collection = bpy.data.collections[export_collection_name]
            bpy.data.collections.remove(old_collection)

        # Create new export collection
        export_collection = bpy.data.collections.new(export_collection_name)
        context.scene.collection.children.link(export_collection)

        log_info(f"Created export collection: {export_collection_name}")
        return export_collection

    def cleanup_export_collection(self, context, export_collection):
        """Clean up the temporary export collection and its objects"""
        if export_collection and export_collection.name in bpy.data.collections:
            # Remove all objects from the collection
            for obj in export_collection.objects:
                bpy.data.objects.remove(obj, do_unlink=True)

            # Remove the collection itself
            bpy.data.collections.remove(export_collection)
            log_info("Cleaned up export collection")

    def prepare_armature_for_export(self, context, original_armature, export_collection):
        """Prepare the existing armature for export by renaming it to 'root'"""
        log_info("Preparing existing armature for export...")

        # Store original name for restoration later
        original_name = original_armature.name
        original_data_name = original_armature.data.name

        # Rename armature to 'root' for export
        original_armature.name = "root"
        original_armature.data.name = "root"

        log_info(f"Renamed armature from '{original_name}' to 'root' for export")
        log_info(f"Armature already has proper bone structure (pelvis → spine_03 → DNA bones)")

        # Store original names for cleanup
        self._original_armature_name = original_name
        self._original_armature_data_name = original_data_name

        return original_armature

    def restore_armature_name(self, context, armature_obj):
        """Restore the original armature name after export"""
        if hasattr(self, '_original_armature_name') and hasattr(self, '_original_armature_data_name'):
            log_info(f"Restoring armature name from 'root' to '{self._original_armature_name}'")
            armature_obj.name = self._original_armature_name
            armature_obj.data.name = self._original_armature_data_name

            # Clean up stored names
            delattr(self, '_original_armature_name')
            delattr(self, '_original_armature_data_name')
        else:
            log_warning("No original armature names stored for restoration")

    def prepare_combined_mesh(self, context, mesh_objects, export_collection, export_armature):
        """Combine all LOD0 meshes into a single mesh like the original MetaHuman FBX"""
        log_info(f"Combining {len(mesh_objects)} mesh objects into single export mesh...")

        if not mesh_objects:
            raise Exception("No mesh objects to combine")

        # Store original selection
        original_selection = context.selected_objects.copy()
        original_active = context.active_object

        try:
            # Ensure we're in object mode
            if context.mode != 'OBJECT':
                bpy.ops.object.mode_set(mode='OBJECT')

            # Clear selection manually
            for obj in context.selected_objects:
                obj.select_set(False)

            # Create copies of all meshes for combination
            mesh_copies = []
            all_materials = []

            for i, original_mesh in enumerate(mesh_objects):
                log_info(f"Processing mesh {i+1}/{len(mesh_objects)}: {original_mesh.name}")

                # Create a copy of the mesh
                mesh_copy = original_mesh.copy()
                mesh_copy.data = original_mesh.data.copy()

                # Remove .001 suffix if present
                clean_name = original_mesh.name.replace('.001', '')
                mesh_copy.name = f"{clean_name}_temp"
                mesh_copy.data.name = f"{clean_name}_temp"

                # Add to scene temporarily for joining
                export_collection.objects.link(mesh_copy)
                mesh_copies.append(mesh_copy)

                # Collect all materials
                for material_slot in mesh_copy.material_slots:
                    if material_slot.material and material_slot.material not in all_materials:
                        all_materials.append(material_slot.material)

            log_info(f"Collected {len(all_materials)} unique materials from all meshes")

            # Select all mesh copies for joining
            for mesh_copy in mesh_copies:
                mesh_copy.select_set(True)

            # Set the first mesh as active (this will be the target for joining)
            context.view_layer.objects.active = mesh_copies[0]

            # Join all meshes into one
            log_info("Joining all meshes into single combined mesh...")
            bpy.ops.object.join()

            # The active object is now the combined mesh
            combined_mesh = context.view_layer.objects.active

            # Rename to match original MetaHuman naming convention
            combined_mesh.name = "MH_Friend_FaceMesh_LOD0"
            combined_mesh.data.name = "MH_Friend_FaceMesh_LOD0"

            # Ensure all materials are assigned to the combined mesh
            for material in all_materials:
                if material.name not in [slot.material.name for slot in combined_mesh.material_slots if slot.material]:
                    combined_mesh.data.materials.append(material)

            # NOTE: Do NOT apply matrix scale here - the join operation preserves the correct scale
            # from the source objects which already have 0.01 matrix scale applied

            # Ensure the combined mesh has proper armature modifier
            self.ensure_armature_modifier(combined_mesh, export_armature)

            log_info(f"Successfully created combined mesh: {combined_mesh.name}")
            log_info(f"Combined mesh stats: {len(combined_mesh.data.vertices)} vertices, {len(combined_mesh.data.polygons)} polygons")
            log_info(f"Materials: {len(combined_mesh.material_slots)}")
            if combined_mesh.data.shape_keys:
                log_info(f"Shape keys: {len(combined_mesh.data.shape_keys.key_blocks)}")
            log_info(f"Vertex groups: {len(combined_mesh.vertex_groups)}")

            return combined_mesh

        except Exception as e:
            log_error(f"Error combining meshes: {str(e)}")
            raise
        finally:
            # Restore original selection manually
            for obj in context.selected_objects:
                obj.select_set(False)

            for obj in original_selection:
                if obj.name in bpy.data.objects:
                    obj.select_set(True)

            if original_active and original_active.name in bpy.data.objects:
                context.view_layer.objects.active = original_active

    def ensure_armature_modifier(self, mesh_obj, armature_obj):
        """Ensure the mesh has a proper armature modifier pointing to the export armature"""
        # Check if armature modifier already exists
        existing_armature_modifier = None
        for modifier in mesh_obj.modifiers:
            if modifier.type == 'ARMATURE':
                existing_armature_modifier = modifier
                break

        if existing_armature_modifier:
            # Update existing modifier to point to export armature
            existing_armature_modifier.object = armature_obj
            log_info(f"Updated existing armature modifier on {mesh_obj.name}: {existing_armature_modifier.name}")
        else:
            # Add new armature modifier
            armature_modifier = mesh_obj.modifiers.new(name="Armature", type='ARMATURE')
            armature_modifier.object = armature_obj
            log_info(f"Added new armature modifier to {mesh_obj.name}")

        # Ensure proper modifier settings for export
        modifier = existing_armature_modifier or armature_modifier
        modifier.use_vertex_groups = True
        modifier.use_bone_envelopes = False

    def create_metahuman_export_hierarchy(self, export_armature, export_collection):
        """Create MetaHuman hierarchy for export with compensation to preserve bone coordinates

        Args:
            export_armature: The armature to export
            export_collection: Collection to add the parent EMPTY to

        Returns:
            tuple: (parent_empty, original_armature_state) for cleanup
        """
        log_info("Creating MetaHuman export hierarchy with compensation...")

        # Store original armature state for restoration
        original_armature_state = {
            'scale': export_armature.scale.copy(),
            'parent': export_armature.parent
        }

        # Create parent EMPTY with 0.01 scale (matches original MetaHuman structure)
        parent_empty = bpy.data.objects.new("MH_Friend_FaceMesh", None)
        parent_empty.empty_display_type = 'PLAIN_AXES'
        parent_empty.empty_display_size = 1.0
        parent_empty.scale = (0.01, 0.01, 0.01)

        # Add EMPTY to export collection
        export_collection.objects.link(parent_empty)

        # COMPENSATE: Scale armature by 100x to counter the 0.01 parent scale
        # This preserves the final bone coordinates: 100x armature × 0.01 parent = 1.0 effective
        export_armature.scale = (100.0, 100.0, 100.0)

        # Parent armature to EMPTY
        export_armature.parent = parent_empty

        log_info(f"Created parent EMPTY '{parent_empty.name}' with 0.01 scale")
        log_info(f"Applied 100x compensation scale to armature")
        log_info("MetaHuman hierarchy: MH_Friend_FaceMesh (0.01) → root (100x) = 1.0 effective")

        return parent_empty, original_armature_state

    def cleanup_metahuman_export_hierarchy(self, export_armature, parent_empty, original_armature_state):
        """Clean up MetaHuman export hierarchy and restore original armature state

        Args:
            export_armature: The armature to restore
            parent_empty: The temporary parent EMPTY to remove
            original_armature_state: Original state to restore
        """
        log_info("Cleaning up MetaHuman export hierarchy...")

        try:
            # Restore original armature state first (most important)
            if export_armature and original_armature_state:
                # Check if armature still exists and is valid
                if export_armature.name in bpy.data.objects:
                    export_armature.parent = original_armature_state['parent']
                    export_armature.scale = original_armature_state['scale']
                    log_info("Restored original armature parent and scale")
                else:
                    log_warning("Export armature no longer exists, skipping restoration")

        except Exception as armature_error:
            log_error(f"Error restoring armature state: {str(armature_error)}")

        try:
            # Remove temporary parent EMPTY (if it still exists and wasn't already removed)
            if parent_empty:
                # Check if the EMPTY still exists in Blender data
                if hasattr(parent_empty, 'name') and parent_empty.name in bpy.data.objects:
                    bpy.data.objects.remove(parent_empty, do_unlink=True)
                    log_info(f"Removed temporary parent EMPTY: {parent_empty.name}")
                else:
                    log_info("Parent EMPTY already removed (likely by export collection cleanup)")

        except Exception as empty_error:
            log_error(f"Error removing parent EMPTY: {str(empty_error)}")

        log_info("MetaHuman export hierarchy cleanup completed")

    # NOTE: Removed apply_metahuman_matrix_scale and restore_metahuman_matrix_scale functions
    # These were causing the 100x scale issue by applying 0.01 scale to objects that already had it
    # Objects imported from DNA already have the correct 0.01 matrix scale applied

# Classes to register
classes = [
    DNA_OT_ExportFBX,
]

def register():
    """Register the operator classes"""
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    """Unregister the operator classes"""
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
